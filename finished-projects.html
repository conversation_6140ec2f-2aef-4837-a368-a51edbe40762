<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finished Projects - Compass Properties</title>
    <meta name="description" content="Browse our portfolio of successfully completed construction and real estate projects that demonstrate our expertise and quality standards.">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'timber-green': '#1A3028',
                        'timber-light': '#3B4F58',
                        'timber-dark': '#0C1916',
                        'tahiti-gold': '#EA8224',
                        'tahiti-light': '#F0A04A',
                        'custom-black': '#050606',
                        'custom-white': '#FBFBFB'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-custom-white text-custom-black">
    <!-- Navigation -->
    <nav class="bg-timber-green shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center">
                    <img src="compass-properties-logo.png" alt="Compass Properties" class="h-10 w-auto">
                    <span class="ml-3 text-custom-white font-bold text-xl">Compass Properties</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="index.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                        <a href="about.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">About</a>
                        <a href="services.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">Services</a>
                        
                        <!-- Projects Dropdown -->
                        <div class="relative group">
                            <button class="text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium flex items-center">
                                Projects <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                                <a href="new-projects.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-tahiti-gold hover:text-white">New Projects</a>
                                <a href="under-construction.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-tahiti-gold hover:text-white">Under Construction</a>
                                <a href="finished-projects.html" class="block px-4 py-2 text-sm text-white bg-tahiti-gold">Finished Projects</a>
                            </div>
                        </div>
                        
                        <a href="construction-updates.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">Updates</a>
                        <a href="media.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">Media</a>
                        <a href="contact.html" class="text-custom-white hover:text-tahiti-gold px-3 py-2 rounded-md text-sm font-medium transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-custom-white hover:text-tahiti-gold focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-timber-green">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="index.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="about.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="services.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Services</a>
                <a href="new-projects.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">New Projects</a>
                <a href="under-construction.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Under Construction</a>
                <a href="finished-projects.html" class="text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Finished Projects</a>
                <a href="construction-updates.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Updates</a>
                <a href="media.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Media</a>
                <a href="contact.html" class="text-custom-white hover:text-tahiti-gold block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="bg-gradient-to-r from-timber-green to-timber-light pt-20 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-custom-white mb-4 fade-in">Finished Projects</h1>
            <p class="text-xl text-custom-white max-w-3xl mx-auto fade-in">
                Explore our portfolio of successfully completed projects that showcase our commitment to excellence and quality craftsmanship.
            </p>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-wrap justify-center gap-4 fade-in">
                <button class="filter-btn active bg-tahiti-gold text-white px-6 py-2 rounded-full font-medium" data-filter="all">All Projects</button>
                <button class="filter-btn bg-white text-timber-green border border-timber-green px-6 py-2 rounded-full font-medium hover:bg-timber-green hover:text-white transition-colors" data-filter="residential">Residential</button>
                <button class="filter-btn bg-white text-timber-green border border-timber-green px-6 py-2 rounded-full font-medium hover:bg-timber-green hover:text-white transition-colors" data-filter="commercial">Commercial</button>
                <button class="filter-btn bg-white text-timber-green border border-timber-green px-6 py-2 rounded-full font-medium hover:bg-timber-green hover:text-white transition-colors" data-filter="mixed">Mixed-Use</button>
            </div>
        </div>
    </section>

    <!-- Projects Gallery -->
    <section class="section-padding bg-custom-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="gallery-grid">
                <!-- Project 1 -->
                <div class="project-item residential image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/C M_Exterior - Elevation.jpg" alt="Luxury Apartment Complex" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Luxury Apartment Complex</h3>
                        <p class="mb-2">Modern residential development with 80 luxury units</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2023</span>
                            <span><i class="fas fa-home mr-1"></i> 80 Units</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm">Residential</span>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="project-item commercial image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/L_Exterior - Crossing.jpg" alt="Corporate Headquarters" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Corporate Headquarters</h3>
                        <p class="mb-2">12-story office building with modern amenities</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2023</span>
                            <span><i class="fas fa-building mr-1"></i> 12 Floors</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">Commercial</span>
                    </div>
                </div>
                
                <!-- Project 3 -->
                <div class="project-item residential image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/L_Exterior - Detail (2).jpg" alt="Garden Villas" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Garden Villas</h3>
                        <p class="mb-2">Premium villas with private gardens and pools</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2022</span>
                            <span><i class="fas fa-home mr-1"></i> 25 Villas</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm">Residential</span>
                    </div>
                </div>
                
                <!-- Project 4 -->
                <div class="project-item mixed image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/L_Exterior - Detail .jpg" alt="Urban Plaza" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Urban Plaza</h3>
                        <p class="mb-2">Mixed-use development with retail and residential</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2022</span>
                            <span><i class="fas fa-store mr-1"></i> Mixed-Use</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">Mixed-Use</span>
                    </div>
                </div>
                
                <!-- Project 5 -->
                <div class="project-item commercial image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/L_Exterior - Detail Street.jpg" alt="Tech Campus" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Tech Campus</h3>
                        <p class="mb-2">Modern technology park with multiple buildings</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2021</span>
                            <span><i class="fas fa-building mr-1"></i> 5 Buildings</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">Commercial</span>
                    </div>
                </div>
                
                <!-- Project 6 -->
                <div class="project-item residential image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/5787255989848887152.jpg" alt="Waterfront Condos" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Waterfront Condos</h3>
                        <p class="mb-2">Luxury condominiums with ocean views</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2021</span>
                            <span><i class="fas fa-home mr-1"></i> 60 Units</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm">Residential</span>
                    </div>
                </div>
                
                <!-- Project 7 -->
                <div class="project-item commercial image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/5787255989848887155.jpg" alt="Medical Center" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Medical Center</h3>
                        <p class="mb-2">State-of-the-art healthcare facility</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2020</span>
                            <span><i class="fas fa-hospital mr-1"></i> Healthcare</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm">Commercial</span>
                    </div>
                </div>
                
                <!-- Project 8 -->
                <div class="project-item mixed image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/5787255989848887159.jpg" alt="City Center" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">City Center</h3>
                        <p class="mb-2">Large mixed-use development in downtown</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2020</span>
                            <span><i class="fas fa-city mr-1"></i> Downtown</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm">Mixed-Use</span>
                    </div>
                </div>
                
                <!-- Project 9 -->
                <div class="project-item residential image-overlay rounded-lg overflow-hidden shadow-lg fade-in" data-searchable>
                    <img src="imgs/5787255989848887160.jpg" alt="Suburban Homes" class="w-full h-64 object-cover gallery-image">
                    <div class="overlay-content">
                        <h3 class="text-xl font-bold mb-2">Suburban Homes</h3>
                        <p class="mb-2">Family-friendly residential community</p>
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <span><i class="fas fa-calendar mr-1"></i> 2019</span>
                            <span><i class="fas fa-home mr-1"></i> 45 Homes</span>
                        </div>
                    </div>
                    <div class="absolute top-4 left-4">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm">Residential</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Awards Section -->
    <section class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 fade-in">
                <h2 class="text-3xl md:text-4xl font-bold text-timber-green mb-4">Awards & Recognition</h2>
                <p class="text-lg text-gray-700 max-w-3xl mx-auto">
                    Our completed projects have received numerous awards and recognition for excellence in design and construction.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Award 1 -->
                <div class="bg-white rounded-lg shadow-lg p-6 text-center fade-in">
                    <div class="text-tahiti-gold text-4xl mb-4">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="text-xl font-bold text-timber-green mb-3">Best Residential Project 2023</h3>
                    <p class="text-gray-700">
                        Luxury Apartment Complex won the Regional Excellence Award for innovative design and sustainability.
                    </p>
                </div>
                
                <!-- Award 2 -->
                <div class="bg-white rounded-lg shadow-lg p-6 text-center fade-in">
                    <div class="text-tahiti-gold text-4xl mb-4">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h3 class="text-xl font-bold text-timber-green mb-3">Green Building Certification</h3>
                    <p class="text-gray-700">
                        Multiple projects certified for environmental sustainability and energy efficiency standards.
                    </p>
                </div>
                
                <!-- Award 3 -->
                <div class="bg-white rounded-lg shadow-lg p-6 text-center fade-in">
                    <div class="text-tahiti-gold text-4xl mb-4">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="text-xl font-bold text-timber-green mb-3">Client Satisfaction Award</h3>
                    <p class="text-gray-700">
                        Recognized for outstanding client service and project delivery excellence across all completed projects.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- WhatsApp Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <a href="https://wa.me/1234567890" target="_blank" class="whatsapp-btn bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110">
            <i class="fab fa-whatsapp text-2xl"></i>
        </a>
    </div>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
    <script>
        // Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const projectItems = document.querySelectorAll('.project-item');
            
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // Update active button
                    filterBtns.forEach(b => {
                        b.classList.remove('active', 'bg-tahiti-gold', 'text-white');
                        b.classList.add('bg-white', 'text-timber-green', 'border', 'border-timber-green');
                    });
                    this.classList.add('active', 'bg-tahiti-gold', 'text-white');
                    this.classList.remove('bg-white', 'text-timber-green', 'border', 'border-timber-green');
                    
                    // Filter projects
                    projectItems.forEach(item => {
                        if (filter === 'all' || item.classList.contains(filter)) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
